# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@remote-ui/async-subscription@^2.1.12", "@remote-ui/async-subscription@^2.1.15":
  version "2.1.15"
  resolved "https://registry.yarnpkg.com/@remote-ui/async-subscription/-/async-subscription-2.1.15.tgz#ab8753770630baa2bc2224e0b5c0ee15c09f5026"
  integrity sha512-SdPfO4ExDOVyOPAoCqT9+Tk0UeU97EMplkxlr5qiv3W7c1yRsxgtbETicoBh9l/P49bLzVjiJG6CQIu1au3Vhw==
  dependencies:
    "@remote-ui/rpc" "^1.4.5"

"@remote-ui/core@^2.2.4":
  version "2.2.4"
  resolved "https://registry.yarnpkg.com/@remote-ui/core/-/core-2.2.4.tgz#56b174b6f041c57d5950811e6f93cfcffb0fbc1a"
  integrity sha512-7ahrsI5GqYvAGcWTcysbKFKbzbhB/mDgIJr9VurF34gor2IqRCrmrPYemLQuOCQhWqPb3AOEUxWFYNLiB5i6kA==
  dependencies:
    "@remote-ui/rpc" "^1.4.5"
    "@remote-ui/types" "^1.1.3"

"@remote-ui/react@^5.0.2":
  version "5.0.4"
  resolved "https://registry.yarnpkg.com/@remote-ui/react/-/react-5.0.4.tgz#9330821455dbd98a214204d034a2abcdb1b6d53a"
  integrity sha512-zrewan3KqKHW8HOCgnpHduG4Ldqyh9ysouGIv8VeE0PlC03eCqi2MFzA+nKIJ1p58LIAbPQ1asp+RwT6ImVImg==
  dependencies:
    "@remote-ui/async-subscription" "^2.1.15"
    "@remote-ui/core" "^2.2.4"
    "@remote-ui/rpc" "^1.4.5"
    "@types/react" ">=17.0.0 <19.0.0"
    "@types/react-reconciler" ">=0.26.0 <0.30.0"

"@remote-ui/rpc@^1.4.5":
  version "1.4.5"
  resolved "https://registry.yarnpkg.com/@remote-ui/rpc/-/rpc-1.4.5.tgz#20328970c314374d96fdaae1cf93aca4b47fefee"
  integrity sha512-Cr+06niG/vmE4A9YsmaKngRuuVSWKMY42NMwtZfy+gctRWGu6Wj9BWuMJg5CEp+JTkRBPToqT5rqnrg1G/Wvow==

"@remote-ui/types@^1.1.3":
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/@remote-ui/types/-/types-1.1.3.tgz#b2a790d5be50fb82d89d4abea9883f87f83a5a25"
  integrity sha512-P1kN1F3p0oMgnLN8Of1Ie9am3sLvJ7nhqHH1pvzkrxqjVwhhyPVZNcwOHyUNZPKp62izhDavdrcnqrdXzVJqGA==

"@shopify/ui-extensions-react@^2024.7.0":
  version "2024.7.0"
  resolved "https://registry.yarnpkg.com/@shopify/ui-extensions-react/-/ui-extensions-react-2024.7.0.tgz#1b22fadedf2b1a00a538871f9b19ad282dac8ee0"
  integrity sha512-NlgZ4A89KjEZQBXyWGpe9TvDgYCGR2f7sCInyiAIEZ1qTdn05wsB5LqKc3BNXMPLswjrT45QmxMdFD2zurLn9w==
  dependencies:
    "@remote-ui/async-subscription" "^2.1.12"
    "@remote-ui/react" "^5.0.2"
    "@types/react" ">=18.2.67"

"@shopify/ui-extensions@^2024.7.0":
  version "2024.7.0"
  resolved "https://registry.yarnpkg.com/@shopify/ui-extensions/-/ui-extensions-2024.7.0.tgz#fc850a1ffdcb6628e9fc8d4b913593dc41b278f0"
  integrity sha512-q+Q2owY78W94FzROYU6RPl7t/soCnCaY49uSbePaxuPd3obydkLKbjSrcyTkzL9obC6rbX6dhx2i15ah8ww22Q==
  dependencies:
    "@remote-ui/async-subscription" "^2.1.12"
    "@remote-ui/core" "^2.2.4"

"@types/prop-types@*":
  version "15.7.12"
  resolved "https://registry.yarnpkg.com/@types/prop-types/-/prop-types-15.7.12.tgz#12bb1e2be27293c1406acb6af1c3f3a1481d98c6"
  integrity sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==

"@types/react-reconciler@>=0.26.0 <0.30.0":
  version "0.28.8"
  resolved "https://registry.yarnpkg.com/@types/react-reconciler/-/react-reconciler-0.28.8.tgz#e51710572bcccf214306833c2438575d310b3e98"
  integrity sha512-SN9c4kxXZonFhbX4hJrZy37yw9e7EIxcpHCxQv5JUS18wDE5ovkQKlqQEkufdJCCMfuI9BnjUJvhYeJ9x5Ra7g==
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@>=17.0.0 <19.0.0", "@types/react@>=18.2.67":
  version "18.3.3"
  resolved "https://registry.yarnpkg.com/@types/react/-/react-18.3.3.tgz#9679020895318b0915d7a3ab004d92d33375c45f"
  integrity sha512-hti/R0pS0q1/xx+TsI73XIqk26eBsISZ2R0wUijXIngRK9R/e7Xw/cXVxQK7R5JjW+SV4zGcn5hXjudkN/pLIw==
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

csstype@^3.0.2:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

"js-tokens@^3.0.0 || ^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

loose-envify@^1.1.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

react-reconciler@^0.29.2:
  version "0.29.2"
  resolved "https://registry.yarnpkg.com/react-reconciler/-/react-reconciler-0.29.2.tgz#8ecfafca63549a4f4f3e4c1e049dd5ad9ac3a54f"
  integrity sha512-zZQqIiYgDCTP/f1N/mAR10nJGrPD2ZR+jDSEsKWJHYC7Cm2wodlwbR3upZRdC3cjIjSlTLNVyO7Iu0Yy7t2AYg==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://registry.yarnpkg.com/scheduler/-/scheduler-0.23.2.tgz#414ba64a3b282892e944cf2108ecc078d115cdc3"
  integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
  dependencies:
    loose-envify "^1.1.0"
