import { useMemo } from "react";
import {
  Banner,
  BlockStack,
  Card,
  Heading,
  Link,
  reactExtension,
  SkeletonText,
  SkeletonTextBlock,
  Text,
  useExtensionEditor,
  useI18n,
  useOrder,
} from "@shopify/ui-extensions-react/customer-account";
import useInvoice from "./hooks/useInvoice";

export default reactExtension("customer-account.order-status.block.render", () => (
  <BlockExtension />
));

function BlockExtension() {
  const order = useOrder();
  const { translate } = useI18n();
  const editor = useExtensionEditor();
  const { invoice, isLoading, status } = useInvoice(order?.id);

  const statusAppearance = useMemo(() => {
    switch (invoice?.status) {
      case "paid":
        return "success";
      case "voided":
        return "subdued";
      default:
        return "warning";
    }
  }, [invoice?.status]);

  if (editor) {
    return (
      <Card padding>
        <BlockStack spacing="base">
          {status === 401 && <Banner status="warning">{translate("error.unauthorized")}</Banner>}

          <BlockStack spacing="extraTight">
            <Heading level={2}>{translate("invoice")} RE-12345</Heading>
            <Text appearance="subdued">
              {translate("status.title")}:{" "}
              <Text appearance="success" emphasis="bold">
                {translate(`status.paid`)}
              </Text>
            </Text>

            <Link to={invoice?.url} external={true}>
              {translate("downloadInvoice")}
            </Link>
          </BlockStack>
        </BlockStack>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card padding>
        <SkeletonText />
        <SkeletonTextBlock />
      </Card>
    );
  }

  if (!invoice) return null;

  if (!["open", "voided", "paid"].includes(invoice.status)) return null;

  return (
    <Card padding>
      <BlockStack spacing="extraTight">
        <Heading level={2}>
          {translate("invoice")} {invoice.title}
        </Heading>
        <Text appearance="subdued">
          {translate("status.title")}:{" "}
          <Text appearance={statusAppearance} emphasis="bold">
            {translate(`status.${invoice.status}`)}
          </Text>
        </Text>

        <Link to={invoice.url} external={true}>
          {translate("downloadInvoice")}
        </Link>
      </BlockStack>
    </Card>
  );
}
