import {
  Button,
  reactExtension,
  useApi,
  useExtensionEditor,
  useI18n,
} from "@shopify/ui-extensions-react/customer-account";
import useInvoice from "./hooks/useInvoice";

export default reactExtension("customer-account.order.action.menu-item.render", () => (
  <MenuActionExtension />
));

function MenuActionExtension() {
  const { translate } = useI18n();
  const { orderId } = useApi();
  const editor = useExtensionEditor();
  const { invoice, isLoading } = useInvoice(orderId);

  if (!invoice && !isLoading && !editor) return null;

  if (editor) {
    return (
      <Button to={invoice?.url} external={true}>
        {translate("downloadInvoice")}
      </Button>
    );
  }

  return (
    <Button to={invoice?.url} external={true} disabled={isLoading} loading={isLoading}>
      {translate("downloadInvoice")}
    </Button>
  );
}
