import { useSessionToken } from "@shopify/ui-extensions-react/customer-account";

export default () => {
  const sessionToken = useSessionToken();

  return async (path, options = {}) => {
    const url = `${process.env.APP_HOME}/${path}`;
    let token;

    // Try to fetch token (not possible in checkout editor)
    try {
      token = await sessionToken.get();
    } catch {
      // Do nothing: Not possible in checkout editor
    }

    return fetch(url, {
      ...options,
      headers: {
        "X-Shopify-Access-Token": `Bearer ${token}`,
        "Content-Type": "application/json",
        ...options.headers,
      },
    });
  };
};
