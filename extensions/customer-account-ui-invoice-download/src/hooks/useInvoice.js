import { useEffect, useState, useRef } from "react";
import useAuthenticatedFetch from "./useAuthenticatedFetch";

export default (orderId = "") => {
  const fetch = useAuthenticatedFetch();
  const [invoice, setInvoice] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [status, setStatus] = useState(null);
  const hasRun = useRef(false);

  useEffect(() => {
    (async () => {
      setIsLoading(true);

      try {
        if (fetch && orderId) {
          if (hasRun.current) return;
          hasRun.current = true;

          const orderIdLast = orderId.split("/").pop();
          const response = await fetch(`/api/ui-extensions/invoice/${orderIdLast}`);
          setStatus(response.status);
          const body = await response.json();

          setInvoice(body);
        }
      } catch (error) {
        console.error(error);
        setError(error);
      } finally {
        setIsLoading(false);
      }
    })();
  }, [fetch, orderId]);

  return {
    invoice,
    isLoading,
    status,
    error,
  };
};
