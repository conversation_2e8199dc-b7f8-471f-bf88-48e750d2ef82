# Learn more about configuring your Customer account UI extension:
# https://shopify.dev/api/customer-account-ui-extensions/unstable/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-04"

[[extensions]]
name = "lexoffice: Rechnung herunterladen"
handle = "lexoffice-invoice-download"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/customer-account-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/MenuActionExtension.jsx"
target = "customer-account.order.action.menu-item.render"

[[extensions.targeting]]
module = "./src/BlockExtension.jsx"
target = "customer-account.order-status.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/customer-account-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/customer-account-ui-extensions/unstable/configuration#network-access
network_access = true
