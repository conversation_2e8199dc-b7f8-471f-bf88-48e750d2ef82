# frozen_string_literal: true

require "rails_helper"

RSpec.describe OAuthRevokeService, type: :service do
  let(:shop) { create(:shop) }
  let(:service) { described_class.new(shop) }

  before do
    stub_request(:post, "#{ENV.fetch("LEXOFFICE_SITE", nil)}/oauth2/revoke")
      .with(
        body: { token: shop.lexoffice_refresh_token },
        headers: { Authorization: service.send(:basic_auth_header, ENV.fetch("LEXOFFICE_KEY", nil),
          ENV.fetch("LEXOFFICE_SECRET", nil)) }
      ).to_return(status: 200, body: "", headers: {})
  end

  describe "#call" do
    context "when successful" do
      it "revokes the connection and clears the tokens" do
        service.call

        shop.reload

        expect(shop.lexoffice_token).to be_nil
        expect(shop.lexoffice_refresh_token).to be_nil
        expect(shop.lexoffice_token_expires_at).to be_nil
      end
    end

    context "when request fails" do
      before do
        stub_request(:post, "#{ENV.fetch("LEXOFFICE_SITE", nil)}/oauth2/revoke")
          .with(
            body: { token: shop.lexoffice_refresh_token },
            headers: { Authorization: service.send(:basic_auth_header, ENV.fetch("LEXOFFICE_KEY", nil),
              ENV.fetch("LEXOFFICE_SECRET", nil)) }
          ).to_raise(RestClient::BadRequest)
      end

      it "notifies the error reporter and clears the tokens" do
        expect(Rails.error).to receive(:report)
          .with(kind_of(RestClient::BadRequest),
            hash_including(context: { tags: "token_revoke_failed", user_id: shop.shopify_domain }))

        service.call

        shop.reload

        expect(shop.lexoffice_token).to be_nil
        expect(shop.lexoffice_refresh_token).to be_nil
        expect(shop.lexoffice_token_expires_at).to be_nil
      end
    end
  end
end
