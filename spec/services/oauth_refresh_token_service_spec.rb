require 'rails_helper'

RSpec.describe OAuthRefreshTokenService do
  describe '#token_expired?' do
    context 'when token expired' do
      let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it 'should return true' do
        expect(service.send(:token_expired?)).to be_truthy
      end
    end

    context 'when token is not expired' do
      let(:shop) { FactoryBot.create(:shop) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it 'should return false' do
        expect(service.send(:token_expired?)).to be_falsey
      end
    end
  end

  describe '#forced?' do
    let(:shop) { FactoryBot.create(:shop, :with_expired_token) }

    context 'when forced' do
      let(:service) { OAuthRefreshTokenService.new(shop, true) }

      it 'should return true' do
        expect(service.forced?).to be_truthy
      end
    end

    context 'when doesnt forced' do
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it 'should return false' do
        expect(service.forced?).to be_falsey
      end
    end
  end

  describe '#lexoffice_token_expires_at' do
    let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
    let(:service) { OAuthRefreshTokenService.new(shop) }

    it 'should return correct value' do
      expect(service.send(:lexoffice_token_expires_at)).to eq shop.lexoffice_token_expires_at
    end
  end

  describe '#lexoffice_refresh_token' do
    let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
    let(:service) { OAuthRefreshTokenService.new(shop) }

    it 'should return correct value' do
      expect(service.send(:lexoffice_refresh_token)).to eq shop.lexoffice_refresh_token
    end
  end

  describe '#call' do
    context 'when token is not expired' do
      let(:shop) { FactoryBot.create(:shop) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it 'should not call refresh_token!' do
        expect(service).not_to receive(:refresh_token!)
        service.call
      end

      context 'when forced' do
        let(:service) { OAuthRefreshTokenService.new(shop, true) }

        before do
          stub_token_refresh
        end

        it 'should call refresh_token!' do
          expect(service).to receive(:refresh_token!)
          service.call
        end
      end
    end

    context 'when token expired' do
      let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      before do
        stub_token_refresh
      end

      it 'should call refresh_token!' do
        expect(service).to receive(:refresh_token!)
        service.call
      end

      context 'when forced' do
        let(:service) { OAuthRefreshTokenService.new(shop, true) }

        it 'should call refresh_token!' do
          expect(service).to receive(:refresh_token!)
          service.call
        end
      end
    end
  end

  describe '#refresh_token!' do
    context 'token refreshed successfully' do
      let(:shop) { FactoryBot.create(:shop) }
      let(:service) { OAuthRefreshTokenService.new(shop) }
      before do
        stub_token_refresh
      end

      it 'should return true' do
        expect(service.send(:refresh_token!)).to be_truthy
      end
    end
  end
end
