# frozen_string_literal: true

require "rails_helper"

RSpec.describe FindInvoiceService do
  let(:shop) { create(:shop) }

  context "with order" do
    let(:order) { build(:order) }

    before do
      allow(ShopifyAPI::Order).to receive(:find).and_return(order)
    end

    context "with existing sync info" do
      let!(:sync_info) do
        create(
          :sync_info,
          shop:,
          shopify_order_id: order.id,
          target_type: "Invoice",
          extra_infos: {
            voucher_title: "Voucher Title",
            status: "open"
          }
        )
      end

      context "with valid params" do
        it "returns http success" do
          res = described_class.call(shop, order.id)

          expect(res[:status]).to eq(:success)
          expect(res).to have_key(:data)
          expect(res[:data][:title]).to eql(sync_info.extra_infos[:voucher_title])
          expect(res[:data][:status]).to eql(sync_info.extra_infos[:status])
          expect(res[:data][:url]).to be_truthy
          expect(res[:data][:url]).to include(order.id.to_s)
        end
      end
    end

    context "without existing sync info" do
      it "returns not found" do
        res = described_class.call(shop, order.id)

        expect(res[:status]).to eq(:not_found)
      end
    end
  end

  context "with invalid order id" do
    it "returns not found" do
      stub_request(:get, %r{orders/invalid_order_id\.json}).to_return(status: :not_found)

      expect do
        described_class.call(shop, "invalid_order_id")
      end.to raise_error(ShopifyAPI::Errors::HttpResponseError)
    end
  end
end
