# frozen_string_literal: true

require "rails_helper"

RSpec.describe TahCreationJob, type: :job do
  let(:shop) { create(:shop) }
  let(:order) { build(:order) }
  let(:order_id) { order.id }
  let(:order_name) { order.name }
  let(:sync_info) { build(:sync_info) }
  let(:invoice_info_target_id) { sync_info.target_id }
  let(:arguments) { { "kind" => "invoice", "transaction_id" => nil } }

  subject(:perform_job) do
    described_class.new.perform(shop.id, order_id, order_name, invoice_info_target_id, arguments)
  end

  before do
    allow(Shop).to receive(:find).with(shop.id).and_return(shop)
    allow(shop).to receive(:with_shopify_session).and_yield
  end

  context "when multiple transaction_keys are present" do
    let(:transaction_key1) { "transaction_key1" }
    let(:transaction_key2) { "transaction_key2" }
    let(:transactions) do
      [
        instance_double("ShopifyAPI::Transaction", status: "success", kind: "sale", authorization: transaction_key1),
        instance_double("ShopifyAPI::Transaction", status: "success", kind: "capture", authorization: transaction_key2)
      ]
    end

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).with(order_id:).and_return(transactions)
    end

    it "does create just one TAH when multiple transactions are present" do
      expect(Lexoffice::TahCreationService).to receive(:call)
      perform_job
    end
  end

  context "when a single valid transaction_key is present" do
    let(:transaction_key) { "transaction_key1" }
    let(:transactions) do
      [
        instance_double("ShopifyAPI::Transaction", status: "success", kind: "sale", authorization: transaction_key)
      ]
    end

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).with(order_id:).and_return(transactions)
    end

    it "calls Lexoffice::TahCreationService for the single valid transaction" do
      expect(Lexoffice::TahCreationService).to receive(:call)
        .with(shop.id, invoice_info_target_id, transaction_key, order_id, order_name)
      perform_job
    end
  end

  context "when no valid transaction_keys are present" do
    let(:transactions) do
      [
        instance_double("ShopifyAPI::Transaction", status: "pending", kind: "sale", authorization: nil),
        instance_double("ShopifyAPI::Transaction", status: "failure", kind: "capture", authorization: "key")
      ]
    end

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).with(order_id:).and_return(transactions)
    end

    it "does not call Lexoffice::TahCreationService" do
      expect(Lexoffice::TahCreationService).not_to receive(:call)
      perform_job
    end
  end

  context "when kind is refund with a single matching transaction" do
    let(:arguments) { { "kind" => "refund", "transaction_id" => 456 } }
    let(:transaction_key) { "refund_transaction_key" }
    let(:transactions) do
      [
        instance_double("ShopifyAPI::Transaction", id: 456, status: "success", kind: "refund",
          authorization: transaction_key)
      ]
    end

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).with(order_id:).and_return(transactions)
    end

    it "calls Lexoffice::TahCreationService with correct parameters" do
      expect(Lexoffice::TahCreationService).to receive(:call).with(shop.id, invoice_info_target_id, transaction_key,
        order_id, order_name)
      perform_job
    end
  end

  context "when transaction has payment_id instead of authorization" do
    let(:payment_id) { "payment_123#" }
    let(:expected_key) { "payment_123" }
    let(:transactions) do
      [
        double(status: "success", kind: "sale", authorization: nil, payment_id:)
      ]
    end

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).with(order_id:).and_return(transactions)
    end

    it "uses payment_id with # removed as transaction key" do
      expect(Lexoffice::TahCreationService).to receive(:call)
        .with(shop.id, invoice_info_target_id, expected_key, order_id, order_name)
      perform_job
    end
  end

  context "when transaction has neither authorization nor payment_id" do
    let(:transactions) do
      [
        double(status: "success", kind: "sale", authorization: nil, payment_id: nil)
      ]
    end

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).with(order_id:).and_return(transactions)
    end

    it "does not call Lexoffice::TahCreationService" do
      expect(Lexoffice::TahCreationService).not_to receive(:call)
      perform_job
    end
  end

  context "when a ShopifyAPI::Errors::HttpResponseError with code 429 occurs" do
    let(:http_response) { ShopifyAPI::Clients::HttpResponse.new(code: 429, body: "", headers: {}) }
    let(:shopify_error) { ShopifyAPI::Errors::HttpResponseError.new(response: http_response) }

    before do
      allow(ShopifyAPI::Transaction).to receive(:all).and_raise(shopify_error)
    end

    it "raises the exception to trigger a retry" do
      expect { perform_job }
        .to raise_error(ShopifyAPI::Errors::HttpResponseError) do |error|
        expect(error.code).to eq(429)
        expect(error.class.to_s).to include("ShopifyAPI::Errors::HttpResponseError")
      end
    end
  end

  context "when a StandardError occurs" do
    let(:error) { StandardError.new("Some error") }

    before do
      allow(shop).to receive(:with_shopify_session).and_raise(error)
      allow(Rails.error).to receive(:set_context)
      allow(Rails.error).to receive(:report)
      allow(ErrorLog).to receive(:handleException)
    end

    it "notifies the Rails error reporter and calls ErrorLog.handleException" do
      expect(Rails.error).to receive(:set_context).with(
        user_id: shop.shopify_domain,
        entry_point: "TahCreationJob",
        params: { order_id:, order_name:, invoice_info_target_id:, arguments: }
      )
      expect(Rails.error).to receive(:report).with(error)
      expect(ErrorLog).to receive(:handleException).with(error, shop.id, kind_of(OpenStruct), order_id)
      perform_job
    end
  end

  context "when sidekiq retries are exhausted" do
    let(:job) { { "args" => [shop.id, order_id, order_name, invoice_info_target_id, arguments] } }
    let(:error) { StandardError.new("Exhausted error") }

    before do
      allow(ErrorLog).to receive(:handleException)
    end

    it "calls ErrorLog.handleException in sidekiq_retries_exhausted block" do
      described_class.sidekiq_retries_exhausted_block.call(job, error)
      expect(ErrorLog).to have_received(:handleException).with(error, shop.id, nil, order_id)
    end
  end
end
