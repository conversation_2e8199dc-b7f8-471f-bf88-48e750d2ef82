# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::SessionTokenController, type: :controller do
  describe "#validate_session_token" do
    let!(:valid_shop) { create(:shop, shopify_domain: "valid_shop_domain.myshopify.com") }

    before do
      request.headers["X-Shopify-Access-Token"] = "Bearer #{token}"
    end

    context "with valid token" do
      let(:token) do
        JWT.encode(
          { dest: valid_shop.shopify_domain },
          ENV.fetch("SHOPIFY_CLIENT_API_SECRET"), "HS256"
        )
      end

      it "validates a valid session token" do
        request.headers["X-Shopify-Access-Token"] = "Bearer #{token}"
        controller.send(:validate_session_token)

        # Expect the shop to be set as an instance variable
        expect(controller.instance_variable_get(:@shop)).to eq(valid_shop)
        expect(controller.send(:current_shop)).to eq(valid_shop)
      end
    end

    context "with invalid token" do
      let(:token) do
        JWT.encode(
          { dest: valid_shop.shopify_domain },
          "wrong_secret", "HS256"
        )
      end

      it "rejects an invalid session token" do
        expect(controller).to receive(:head).with(:unauthorized)

        controller.send(:validate_session_token)

        # Expect the shop to be nil
        expect(controller.instance_variable_get(:@shop)).to eq(nil)
      end
    end

    context "with missing token" do
      let(:token) { nil }

      it "rejects a missing session token" do
        expect(controller).to receive(:head).with(:unauthorized)

        controller.send(:validate_session_token)
      end
    end
  end
end
