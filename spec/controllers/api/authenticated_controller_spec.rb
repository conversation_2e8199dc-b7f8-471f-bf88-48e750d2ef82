# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::AuthenticatedController, type: :controller do
  controller do
    def index
      head :ok
    end
  end

  let(:shop) { create(:shop) }

  before do
    controller.instance_variable_set(:@current_shop, shop)
  end

  describe "before_action update_shop_access_scopes" do
    it "calls update_access_scopes on the shop" do
      expect(shop).to receive(:add_or_update_access_scopes)
      get :index
    end

    it "does not call update_access_scopes when no session exists" do
      controller.instance_variable_set(:@current_shop, nil)
      expect(shop).not_to receive(:add_or_update_access_scopes)
      get :index
    end
  end

  describe "before_action check_app_was_reinstalled" do
    context "when app was marked as uninstalled" do
      before do
        shop.update(uninstalled_at: Time.zone.at(0))
      end

      it "calls update on the shop" do
        expect(shop).to receive(:update!).with(uninstalled_at: nil)
        get :index
      end
    end

    it "does not call update when uninstalled_at is nil" do
      shop.update(uninstalled_at: nil)
      expect(shop).not_to receive(:update!)
      get :index
    end

    it "does not call update when no session exists" do
      controller.instance_variable_set(:@current_shop, nil)
      expect(shop).not_to receive(:update!)
      get :index
    end
  end
end
