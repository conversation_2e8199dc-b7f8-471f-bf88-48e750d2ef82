# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::UiExtensions::CustomerController, type: :controller do
  let(:shop) { create(:shop) }
  let(:order) { build(:order) }

  before do
    allow(shop).to receive(:billing_plan).and_return(billing_plan)
    allow(controller).to receive(:validate_session_token).and_return(true)
    allow(controller).to receive(:current_shop).and_return(shop)
  end

  describe "GET #show_invoice" do
    context "with customer_accounts_invoice_download disabled" do
      let(:billing_plan) { create(:billing_plan, features: []) }

      it "returns unauthorized" do
        get :show_invoice, params: { order_id: order.id }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with customer_accounts_invoice_download enabled" do
      let(:billing_plan) { create(:billing_plan, features: ["customer_accounts_invoice_download"]) }

      context "not found" do
        it "returns not found" do
          allow(FindInvoiceService).to receive(:call).and_return({ status: :not_found })

          get :show_invoice, params: { order_id: order.id }
          expect(response).to have_http_status(:not_found)
        end
      end

      context "success" do
        it "returns success" do
          allow(FindInvoiceService).to receive(:call).and_return({ status: :success, data: { asd: "qwe" } })

          get :show_invoice, params: { order_id: order.id }
          expect(response).to have_http_status(:success)
          expect(response.parsed_body).to eq({ "asd" => "qwe" })
        end
      end
    end
  end
end
