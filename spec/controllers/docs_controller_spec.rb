# frozen_string_literal: true

require "rails_helper"

RSpec.describe DocsController, type: :controller do
  describe "GET #download_pdf" do
    let(:order_id) { "12345" }
    let(:valid_token) { "abc123" }
    let(:sync_info) { create(:sync_info, shopify_order_id: order_id, target_type: "Invoice") }
    let(:invoice) { { binary: "pdf_content", filename: "invoice.pdf" } }

    before do
      allow(SyncInfo).to receive(:find_by).and_return(sync_info)
      allow(Lexoffice::RenderInvoicePdfService).to receive(:call).and_return(invoice)
    end

    context "when sync_info is found and token is valid" do
      before do
        allow(sync_info).to receive(:target_id).and_return("target123")
        allow(sync_info).to receive(:digest).and_return(valid_token)
      end

      it "sends the PDF data" do
        get :download_pdf, params: { order_id:, token: valid_token }
        expect(response).to have_http_status(:success)
        expect(response.body).to eq("pdf_content")
        expect(response.headers["Content-Type"]).to eq("application/pdf")
        expect(response.headers["Content-Disposition"]).to include("invoice.pdf")
      end
    end

    context "when sync_info is not found" do
      before do
        allow(SyncInfo).to receive(:find_by).and_return(nil)
      end

      it "returns not_found status" do
        get :download_pdf, params: { order_id:, token: valid_token }
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when sync_info is found but target_id is nil" do
      before do
        allow(sync_info).to receive(:target_id).and_return(nil)
      end

      it "returns not_found status" do
        get :download_pdf, params: { order_id:, token: valid_token }
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when token is invalid" do
      before do
        allow(sync_info).to receive(:target_id).and_return("target123")
      end

      it "returns forbidden status" do
        get :download_pdf, params: { order_id:, token: "invalid_token" }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end
end
