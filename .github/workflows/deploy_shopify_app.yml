name: Deploy Shopify configs and extensions

on:
  workflow_call:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up node
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install

      - name: Set Environment Variables
        run: |
          echo "$GITHUB_REF"
          if [ "$GITHUB_REF" == "refs/heads/staging" ]; then
            echo "Using staging configuration"
            echo "APP_HOME=${{ secrets.APP_HOME_STAGING }}" >> $GITHUB_ENV
            echo "CONFIG_NAME=staging" >> $GITHUB_ENV
          elif [ "$GITHUB_REF" == "refs/heads/main" ]; then
            echo "Using production configuration"
            echo "APP_HOME=${{ secrets.APP_HOME_PRODUCTION }}" >> $GITHUB_ENV
            echo "CONFIG_NAME=production" >> $GITHUB_ENV
          fi

      - name: Deploy Shopify app configuration, extensions, etc.
        env:
          SHOPIFY_CLI_PARTNERS_TOKEN: ${{ secrets.SHOPIFY_CLI_PARTNERS_TOKEN }}
          COMMIT_URL: ${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}
        run: |
          yarn run shopify app deploy --force --config "$CONFIG_NAME" --source-control-url "$COMMIT_URL"