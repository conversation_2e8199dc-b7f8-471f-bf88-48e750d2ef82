{"name": "lexoffice-app", "scripts": {"build": "vite build", "dev": "vite", "coverage": "vitest run --coverage", "lint": "eslint 'frontend/**/*.{js,jsx}'"}, "dependencies": {"@emotion/react": "^11.13.0", "@honeybadger-io/js": "^6.9.3", "@honeybadger-io/react": "^6.1.23", "@rails/actioncable": "^7.2.0", "@shopify/app-bridge": "^3.1.0", "@shopify/app-bridge-react": "^3.1.0", "@shopify/app-bridge-utils": "^3.1.0", "@shopify/polaris": "^12.0.0", "@shopify/polaris-icons": "^6.16.0", "@shopify/stylelint-polaris": "^5.1.0", "html-react-parser": "^5.1.12", "i18next": "^23.12.3", "lodash.debounce": "^4.0.8", "react": "^18.3.1", "react-awesome-reveal": "^4.2.13", "react-dom": "^18.3.1", "react-html-parser": "^2.0.2", "react-i18next": "^15.0.1", "react-query": "^3.39.3", "react-router-dom": "^6.26.0", "react-youtube": "^10.1.0", "sass": "^1.77.8"}, "devDependencies": {"eslint": "^8.57.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-import": "^2.29.1", "eslint-import-resolver-alias": "^1.1.2", "prettier": "^3.4.2", "stylelint": "^16.6.1", "@vitejs/plugin-react": "^4.3.1", "vite": "4.3.0", "vite-plugin-rails": "^0.5.0", "vite-plugin-ruby": "3.2.0"}}