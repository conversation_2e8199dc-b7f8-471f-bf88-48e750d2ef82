<dl class="settings-table">
  <dt><%= t('support.settings.create_invoices')%></dt>
  <dd><%= shop_settings.create_invoices %></dd>
  <dt><%= t('support.settings.mark_due_immediately')%></dt>
  <dd><%= shop_settings.mark_due_immediately %></dd>
  <dt><%= t('support.settings.invoice_title')%></dt>
  <dd><%= shop_settings.invoice_title %></dd>
  <dt><%= t('support.settings.invoice_pretext')%></dt>
  <dd><%= shop_settings.invoice_pretext %></dd>
  <dt><%= t('support.settings.invoice_posttext')%></dt>
  <dd><%= shop_settings.invoice_posttext %></dd>
  <dt><%= t('support.settings.mail_invoice')%></dt>
  <dd><%= shop_settings.send_invoice_mail %></dd>
  <dt><%= t('support.settings.invoice_mail_subject')%></dt>
  <dd><%= shop_settings.invoice_mail_subject %></dd>
  <dt><%= t('support.settings.invoice_mail_body')%></dt>
  <dd><%= shop_settings.invoice_mail_body %></dd>
  <dt><%= t('support.settings.credit_note_mail_subject')%></dt>
  <dd><%= shop_settings.credit_note_mail_subject %></dd>
  <dt><%= t('support.settings.credit_note_mail_body')%></dt>
  <dd><%= shop_settings.credit_note_mail_body %></dd>
  <dt><%= t('support.settings.createRefunds')%></dt>
  <dd><%= shop_settings.create_refunds %></dd>
  <dt><%= t('support.settings.refund_title')%></dt>
  <dd><%= shop_settings.refund_title %></dd>
  <dt><%= t('support.settings.refund_pretext')%></dt>
  <dd><%= shop_settings.refund_pretext %></dd>
  <dt><%= t('support.settings.refund_posttext')%></dt>
  <dd><%= shop_settings.refund_posttext %></dd>
  <dt><%= t('support.settings.invoice_language')%></dt>
  <dd><%= shop_settings.invoice_language %></dd>
  <dt><%= t('support.settings.shipping_min_days')%></dt>
  <dd><%= shop_settings.shipping_min_days %></dd>
  <dt><%= t('support.settings.shipping_max_days')%></dt>
  <dd><%= shop_settings.shipping_max_days %></dd>
  <dt><%= t('support.settings.shipping_types')%></dt>
  <dd><%= shop_settings.shipping_type %></dd>
  <dt><%= t('support.settings.enable_tender_transactions')%></dt>
  <dd><%= shop_settings.enable_tender_transactions %></dd>
  <dt><%= t('support.settings.tah_creation')%></dt>
  <dd><%= shop_settings.enable_tah_creation %></dd>
  <dt><%= t('support.settings.calculate_shipping_tax')%></dt>
  <dd><%= shop_settings.calculate_shipping_tax %></dd>
  <dt><%= t('support.settings.use_SKUs')%></dt>
  <dd><%= shop_settings.use_SKUs %></dd>
  <dt><%= t('support.settings.confirm_tax_settings')%></dt>
  <dd><%= shop_settings.confirm_tax_settings %></dd>
  <dt><%= t('support.settings.email_tags')%></dt>
  <dd><%= shop_settings.mail_exclusion_tags %></dd>
  <dt><%= t('support.settings.order_tags')%></dt>
  <dd><%= shop_settings.order_exclusion_tags %></dd>
</dl>

<h4>Letzte Änderungen</h4>
<%= table_for (shop_settings.audits + transaction_settings.audits).sort_by { |item| -item.created_at.to_i }, :table_html => { :class => "table-shop-settings table table-hover table-bordered" },
              :sortable => false,
              :data_row_html => {
                :class => lambda {cycle('lexoffice_orange_light')}} do |table| %>
  <% table.column :created_at,  :header => "Geändert am" %>
  <% table.column :audited_changes,  :header => "Änderungen" %>

  <% table.define :footer do %>
    <tfoot>
    <tr>
      <td colspan="<%= table.columns.length %>">
      </td>
    </tr>
    </tfoot>
  <% end %>
<% end %>
