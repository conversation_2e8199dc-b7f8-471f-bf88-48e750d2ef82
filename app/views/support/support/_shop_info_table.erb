<%= table_for @shop_info, :table_html => { :class => "table-sync-infos table table-hover table-bordered" },
              :sortable => false,
              :data_row_html => {
                  :class => lambda {cycle('lexoffice_orange_light')}} do |table| %>
  <% table.column :name, :header => "Shop Name", :link_url => lambda { |info| "https://#{@current_shop.shopify_domain}" }, :link_html => { "target" => "_blank" } %>
  <% table.column :import_unlocked?,  :header => "Import freigeschaltet"     %>
  <% table.column :data => lambda {
      @current_shop.billing_plan&.name
    },  :header => "Plan" %>
  <% table.column :data => lambda {
      link_to @current_shop.internal_test_shop ? "Test Shop zurücksetzen (Aktiv)" : "Als Test Shop markieren (Inaktiv)",
              "/support/toggle_internal_test_shop?shop_id=#{@current_shop.id}&internal_test_shop=#{!@current_shop.internal_test_shop}",
              method: :post,
              remote: true,
              class: @current_shop.internal_test_shop ? "btn btn-success" : "btn btn-basic"
    }, :header => "Internal Test Shop" %>
  <% table.define :footer do %>
    <tfoot>
    <tr>
      <td colspan="<%= table.columns.length %>">

      </td>
    </tr>
    </tfoot>
  <% end %>
<% end %>
