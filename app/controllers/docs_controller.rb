# frozen_string_literal: true

class DocsController < ApplicationController
  layout false
  # checks if the order id and name match and if they do, pulls the pdf from lexoffice
  # via the SyncInfo target id object and sends it to the user
  def download_pdf
    sync_info = SyncInfo.find_by(shopify_order_id: params.require(:order_id), target_type: "Invoice")

    return head :not_found if sync_info.nil? || sync_info.target_id.nil?
    return head :forbidden if sync_info.digest != params.require(:token)

    invoice = Lexoffice::RenderInvoicePdfService.call(sync_info)

    send_data(invoice[:binary], filename: invoice[:filename], type: "application/pdf")
  end
end
