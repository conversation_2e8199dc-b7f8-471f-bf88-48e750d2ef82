# frozen_string_literal: true

class WebhooksController < ApplicationController
  include ShopifyApp::WebhookVerification
  before_action :find_current_shop, except: %i[uninstall shop_redact customer_redact customer_data_request]
  before_action :check_plan, except: %i[uninstall shop_redact customer_redact customer_data_request]

  ShopifyObject = Struct.new(:id, :name, :shopify_type)

  def uninstall
    current_shop = Shop.find_by(shopify_domain: params[:myshopify_domain])
    BillingServices::UninstallService.call(current_shop) if current_shop.present?
    head :ok
  rescue StandardError
    head :ok
  end

  # GDPR webhooks----------------------------------------------------
  def customer_redact
    head :ok
  end

  def shop_redact
    head :ok
  end

  def customer_data_request
    head :ok
  end

  # orders-----------------------
  def orders_create
    rules_count = SyncRule.order_rules.with_live_sync.for_shop(@current_shop.id).count
    SyncOrderJob.perform_async('orders/create', params[:id], @current_shop.id) if rules_count.positive?
    head :ok
  end

  def orders_fulfilled
    rules_count = SyncRule.order_rules.with_live_sync.for_shop(@current_shop.id).count
    SyncOrderJob.perform_async('orders/fulfilled', params[:id], @current_shop.id) if rules_count.positive?
    head :ok
  end

  def refunds_create
    rules_count = SyncRule.refund_rules.with_live_sync.for_shop(@current_shop.id).count
    RefundJob.perform_async('refunds/create', params[:id], params[:order_id], @current_shop.id) if rules_count.positive?
    head :ok
  end

  def tender_transactions_create
    rules_count = SyncRule.tender_transaction_rules.with_live_sync.for_shop(@current_shop.id).count
    # we need to send the tender transaction params directly, because Shopify API does not allow a lookup for a specific
    # transaction.
    # sidekiq requires params serialized to JSON
    TenderTransactionJob.perform_async(tender_transaction_attrs.to_json, @current_shop.id) if rules_count.positive?
    head :ok
  end

  private

  def find_current_shop
    @current_shop = Shop.find_by(shopify_domain: shop_domain)
  end

  def check_plan
    return if @current_shop&.plan_active?

    head :ok
  end

  def shop_domain
    request.headers['HTTP_X_SHOPIFY_SHOP_DOMAIN']
  end

  def tender_transaction_attrs
    params.permit(:id, :order_id, :amount, :processed_at, :remote_reference, :payment_method)

    {
      id: params[:id],
      order_id: params[:order_id],
      amount: params[:amount],
      processed_at: params[:processed_at],
      remote_reference: params[:remote_reference],
      payment_method: params[:payment_method]
    }
  end
end
