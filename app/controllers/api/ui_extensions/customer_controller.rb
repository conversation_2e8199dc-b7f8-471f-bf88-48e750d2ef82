# frozen_string_literal: true

module Api
  module UiExtensions
    class CustomerController < ::Api::SessionTokenController
      before_action :check_plan

      def show_invoice
        order_id = params.fetch(:order_id)
        result = FindInvoiceService.call(current_shop, order_id)

        case result[:status]
        when :not_found
          head :not_found
        when :success
          render json: result[:data]
        end
      end

      private

      def check_plan
        return if current_shop.feature_enabled?("customer_accounts_invoice_download")

        head :unauthorized
      end
    end
  end
end
