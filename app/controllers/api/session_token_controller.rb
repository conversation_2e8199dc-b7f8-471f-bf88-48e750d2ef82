# frozen_string_literal: true

module Api
  class SessionTokenController < ApplicationController
    before_action :validate_session_token
    skip_before_action :verify_authenticity_token

    def validate_session_token
      token = request.headers["X-Shopify-Access-Token"]&.split&.last
      return head :unauthorized if token.blank?

      begin
        data, _header = JWT.decode token, ENV.fetch("SHOPIFY_CLIENT_API_SECRET"), true, { algorithm: "HS256" }
        @shop = Shop.find_by(shopify_domain: data["dest"])
      rescue JWT::DecodeError => e
        Rails.error.report(e)
        return head :unauthorized
      end

      head :unauthorized if @shop.blank?
    end

    def current_shop
      @shop
    end
  end
end
