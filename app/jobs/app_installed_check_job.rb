# frozen_string_literal: true

# Simple job class to check if a specific shop is still installed by making a request to the Shopify API
# If the shop is not found, it updates the uninstalled_at timestamp to the current time
#
# @param [Integer] shop_id The ID of the shop to check
class AppInstalledCheckJob
  include Sidekiq::Job

  def perform(attrs)
    shop = Shop.find(attrs["shop_id"])
    return if shop.uninstalled_at.present?

    begin
      shop.with_shopify_session { ShopifyAPI::Shop.current }
    rescue ShopifyAPI::Errors::HttpResponseError => e
      shop.update!(uninstalled_at: Time.zone.at(0)) if [401, 404].include?(e.response.code)
    end
  end
end
