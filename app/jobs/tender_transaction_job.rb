# frozen_string_literal: true

require "ostruct"

class PaymentMethodNotFound < StandardError; end

class TenderTransactionJob < FinancialBaseJob
  include Sidekiq::Worker
  sidekiq_options retry: 3, queue: "live"
  sidekiq_retries_exhausted do |job, ex|
    transaction_attr = job["args"][0]
    shop_id = job["args"][1]
    transaction_params = JSON.parse(transaction_attr)

    context = {
      user_id: shop_id,
      class: job["class"],
      jid: job["jid"],
      transaction_id: transaction_params["id"],
      order_id: transaction_params["order_id"],
      tags: ex.respond_to?(:code) && ex.code == 429 ? "too_many_requests_tender_transactions" : "tender_transactions"
    }

    Rails.error.report(ex, context:)

    log_entity = OpenStruct.new(id: transaction_params["id"],
      name: nil,
      shopify_type: "tender_transaction",
      retry_params: transaction_attr)
    ErrorLog.handleException(ex, shop_id, log_entity, transaction_params["order_id"])
  end

  # rubocop:disable Metrics/CyclomaticComplexity
  # rubocop:disable Metrics/PerceivedComplexity
  # rubocop:disable Metrics/MethodLength
  def perform(transaction_attr, shop_id, import = false, locale = "de") # rubocop:disable Style/OptionalBooleanParameter
    I18n.locale = locale
    @transfer_success = false
    transaction_params = JSON.parse(transaction_attr)
    return if transaction_params["payment_method"].casecmp("paypal").zero?

    shop = Shop.includes(:shop_setting, :transaction_setting).find(shop_id)
    set_error_context(
      user_id: shop.shopify_domain,
      params: { transaction_attr:, import:, locale: },
      tags: "tender_transaction"
    )

    # raise error if not connected to lexoffice
    raise StandardError, "Unauthorized" unless shop.connected_to_lexoffice?

    order = shop.with_shopify_session { ShopifyAPI::Order.find(id: transaction_params["order_id"]) }

    # return if order is POS order and POS orders are excluded
    return if shop.shop_setting.excludePOS && (order.source_name == "pos")

    if !is_a?(SkippedRetry::RetryTenderTransactionJob) &&
        EvaluateAndExcludeEntityService.call(order, shop, "Transaction", transaction_params["id"]).nil?
      return
    end

    if transaction_params["amount"].to_d.negative?
      FindOrCreatePendingRefundService.call(transaction_params["order_id"], transaction_params["amount"], shop)
    end

    # return if tender transactions are not enabled or it's an import
    return unless shop.shop_setting.enable_tender_transactions || import

    shop.refresh_token_if_expired
    # at this point extra payment methods are synchronised, except paypal
    # extra payment methods have another process defined in the service class FindAndMapExtraFinancialAccountsService
    if ALL_TYPES.exclude?(transaction_params["payment_method"].downcase) && !order.nil? &&
        !transaction_params["payment_method"].casecmp("paypal").zero?
      transaction_params["payment_method"] =
        FindAndMapExtraFinancialAccountsService.call(transaction_params["amount"].to_d, shop, order)
    end

    WithLockService.call("financial_account_service_lock_#{shop.id}", context: { user_id: shop.shopify_domain }) do
      FindOrEnsureFinancialAccountService.call(shop.transaction_setting.reload, shop.lexoffice_token,
        [transaction_params["payment_method"]])
    end
    FindOrCreateFinancialTransactionService.call(shop, shop.transaction_setting, transaction_params)
    @transfer_success = true
  rescue Redlock::LockError => e
    # re-raise lock errors, so the job can be requeued without incrementing the retry count
    # requeue is handled in the middleware
    raise e
  rescue StandardError => e
    raise e if e.class.to_s.include?("ShopifyAPI::Errors::HttpResponseError") && e.code == 429

    Rails.error.report(e)
    log_entity = OpenStruct.new(id: JSON.parse(transaction_attr)["id"],
      name: nil,
      shopify_type: "tender_transaction",
      retry_params: transaction_attr)
    ErrorLog.handleException(e, shop_id, log_entity, JSON.parse(transaction_attr)["order_id"])

    raise e if import
  end
  # rubocop:enable Metrics/CyclomaticComplexity
  # rubocop:enable Metrics/PerceivedComplexity
  # rubocop:enable Metrics/MethodLength
end
