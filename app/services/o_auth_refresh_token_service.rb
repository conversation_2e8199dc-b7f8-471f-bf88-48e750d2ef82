# frozen_string_literal: true

class OAuthRefreshTokenService
  attr_reader :shop, :force

  alias forced? force

  def initialize(shop, force = false)
    @shop = shop
    @force = force
  end

  def call
    # Quick check without lock first
    return unless token_expired? || forced?

    WithLockService.call("token_refresh_lock_#{@shop.id}", context: { user_id: @shop.shopify_domain }) do
      # Reload and recheck after getting lock to ensure we still need to refresh
      shop.reload
      return unless token_expired? || forced?

      refresh_token!
    end
  end

  private

  def refresh_token!
    response = Lexoffice::TokenService.new(lexoffice_refresh_token).call
    response = JSON.parse(response)

    shop.update(
      lexoffice_token: response["access_token"],
      lexoffice_refresh_token: response["refresh_token"],
      lexoffice_token_expires_at: Time.zone.now + response["expires_in"].to_i
    )
  rescue RestClient::BadRequest => e
    Rails.error.report(e, context: { tags: "token_refresh_failed", user_id: shop.shopify_domain, response: e.response })
    if JSON.parse(e.response)&.fetch("error") == "invalid_grant"
      shop.mark_for_connection_reset
      shop.send_mail_for_connection_reset
    end
  end

  def token_expired?
    return true if lexoffice_token_expires_at.nil?

    Time.zone.at(lexoffice_token_expires_at) - ENV.fetch("TOKEN_BUFFER_MINUTES", 5).to_i.minutes < Time.zone.now
  end

  def lexoffice_refresh_token
    shop.reload.lexoffice_refresh_token
  end

  def lexoffice_token_expires_at
    shop.reload.lexoffice_token_expires_at
  end
end
