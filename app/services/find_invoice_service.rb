# frozen_string_literal: true

class FindInvoiceService < ApplicationService
  def initialize(shop, order_id)
    super()
    @shop = shop
    @order_id = order_id
  end

  def call
    @shop.with_shopify_session do
      @order = ShopifyAPI::Order.find(id: @order_id)
      sync_info = SyncInfo.for_shop(@shop.id).find_by(shopify_order_id: @order_id, target_type: "Invoice")

      return { status: :not_found } if sync_info.nil? || sync_info.target_id.nil? || @shop.lexoffice_token.blank?

      {
        status: :success,
        data: {
          url: invoice_url,
          title: sync_info.extra_infos[:voucher_title],
          status: sync_info.extra_infos[:status]
        }
      }
    end
  end

  private

  def invoice_url
    "#{ENV.fetch("APP_HOME")}/docs/#{@order.id}/#{digest}"
  end

  def digest
    Digest::SHA1.hexdigest("#{@order.id}|#{@order.name}")
  end
end
