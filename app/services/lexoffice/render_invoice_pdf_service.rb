# frozen_string_literal: true

module Lexoffice
  # service class to prepare data for contact creation
  class RenderInvoicePdfService < ApplicationService
    def initialize(sync_info)
      super()
      @sync_info = sync_info
      @shop = @sync_info.shop
    end

    def call
      @shop.refresh_token_if_expired
      invoice_endpoint = Lexoffice::Invoice.new(@shop.lexoffice_token)

      pdf_binary = invoice_endpoint.pdf(@sync_info.target_id)
      filename = invoice_endpoint.voucher_number(@sync_info.target_id).insert(2, "-") || "Rechnung"
      filename += ".pdf" unless filename.end_with?(".pdf")

      {
        filename:,
        binary: Base64.decode64(pdf_binary)
      }
    end
  end
end
