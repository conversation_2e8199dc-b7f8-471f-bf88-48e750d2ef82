# frozen_string_literal: true

module Lexoffice
  # This Service is used to get a new token from the Lexoffice API
  class TokenService
    attr_reader :lexoffice_refresh_token

    def initialize(lexoffice_refresh_token)
      @lexoffice_refresh_token = lexoffice_refresh_token
    end

    def call
      perform_request
    end

    private

    def perform_request
      RestClient.post "#{ENV['LEXOFFICE_SITE']}/oauth2/token",
                      { grant_type: 'refresh_token', refresh_token: lexoffice_refresh_token },
                      { Authorization: basic_auth_header(ENV['LEXOFFICE_KEY'], ENV['LEXOFFICE_SECRET']) }
    end

    def basic_auth_header(client_id, client_secret)
      "Basic #{Base64.encode64("#{client_id}:#{client_secret}").delete("\n")}"
    end
  end
end
