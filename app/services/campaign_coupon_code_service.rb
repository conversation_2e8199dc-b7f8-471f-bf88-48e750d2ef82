# frozen_string_literal: true

class CampaignCouponCodeService

  def initialize(number_of_coupons, free_days, redeem_counter, validity, individual_prefix)
    @number_of_coupons = number_of_coupons
    @free_days = free_days
    @redeem_counter = redeem_counter
    @validity = validity
    @individual_prefix = individual_prefix
  end

  def call
    characters = %w[A B C D E F G H J K L M P Q R T W X Y Z 0 1 2 3 4 5 6 7 8 9]
    code = ''

    @number_of_coupons.to_i.times do
      code = @individual_prefix
      6.times { code += characters.sample }
      ShopifyBilling::CampaignCouponCode.create!({ coupon_code: code, free_days: @free_days, redeemed: false,
                                                   redeem_counter: @redeem_counter, validity: @validity })
      code = ''
    end
  end
end
