# frozen_string_literal: true

# This services transforms the transfers into a format that the frontend expects.
class TransfersTransformerService < ApplicationService
  def initialize(transfers)
    @transfers = transfers
  end

  def call
    @transfers.map do |transfer|
      collections = transform_types(transfer)
      collections[:errors] = []
      map_error_messages(transfer, collections[:errors])

      {
        id: transfer[:shopify_order_id],
        url: "/orders/#{transfer[:shopify_order_id]}",
        order_id: transfer[:shopify_order_names],
        date: transfer[:shopify_created_at],
        invoices: collections[:invoices],
        credit_notes: collections[:credit_notes],
        transactions: collections[:transactions],
        errors: collections[:errors]
      }
    end
  end

  private

  def transform_types(transfer)
    transfer[:target_types].each_with_index.with_object(invoices: [],
                                                        credit_notes: [],
                                                        transactions: []) do |(type, index), collections|
      extra_info = transfer[:extra_infos].try('[]', index)&.with_indifferent_access
      target_id = transfer[:target_ids].try('[]', index)
      last_action = transfer[:last_action].try('[]', index)
      sync_info_id = transfer[:sync_info_ids].try('[]', index)

      case type
      when 'Invoice'
        collections[:invoices] << { title: extra_info&.dig('voucher_title'),
                                    sync_info_id:,
                                    status: extra_info&.dig('status'),
                                    doc_id: extra_info&.dig('document_id'),
                                    target_id:,
                                    last_action: }
      when 'Refund'
        collections[:credit_notes] << { title: extra_info&.dig('voucher_title'),
                                        sync_info_id:,
                                        status: extra_info&.dig('status'),
                                        doc_id: extra_info&.dig('document_id'),
                                        target_id:,
                                        last_action: }
      when 'Transaction'
        collections[:transactions] << { amount: extra_info&.dig('amount'), target_id:, last_action:, sync_info_id: }
      end
    end
  end

  def map_error_messages(transfer, errors)
    transfer[:error_messages]&.each_with_index do |error, index|
      next if error.blank?

      errors << { title: error,
                  doc_type: transfer[:target_types].try('[]', index),
                  error_id: transfer[:error_ids].try('[]', index),
                  helpscout_id: transfer[:error_helpscout_id].try('[]', index),
                  error_type: transfer[:type].try('[]', index) }
    end
  end
end
